/**
 * Specificity Gate - Fourth and final gate in the cascade flow
 *
 * Runs a refinement loop (max 3 iterations) to ensure concrete details on:
 * - Problem statement (< 160 chars starting with verb)
 * - Core feature (singular statement detected by "the core feature is" or one bullet)
 * - Target user (explicit persona with regex pattern)
 * - Success metrics (contains % / $, "KPI", "metric", or quantifier)
 */

import { mutation } from "../../_generated/server";
import { v } from "convex/values";
import type { MutationCtx } from "../../_generated/server";
import type { Id } from "../../_generated/dataModel";
import {
  type DetailedGateResult,
  type GateContext,
  type GateFailureReason
} from "../sharedTypes";



/**
 * Question bank for missing criteria - keyed to missing items
 */
const CLARIFICATION_QUESTIONS = {
  missing_target_user: [
    "Who exactly is your target user? (e.g., 'busy small business owners', 'freelance designers', 'startup founders')",
    "What type of person or organization would use this product?",
    "Can you describe your ideal customer in more detail?"
  ],
  missing_core_feature: [
    "What is the ONE main thing users will do with your product?",
    "If you had to describe the core feature in one sentence, what would it be?",
    "What's the primary action or capability your product provides?"
  ],
  missing_success_metrics: [
    "How will you measure if this product is successful?",
    "What specific metrics or outcomes would indicate success? (e.g., '20% increase in productivity', '$10k monthly revenue')",
    "What would 'winning' look like for this product?"
  ],
  missing_problem_statement: [
    "What specific problem does this solve for your users?",
    "What pain point or frustration are you addressing?",
    "What struggle do your target users currently face that this product would eliminate?"
  ]
};

/**
 * Claude-powered specificity analysis
 * Uses Claude 3.5 Sonnet with temperature 0.3 for deterministic analysis
 */
async function analyzeSpecificityWithClaude(
  userInput: string,
  previousMessages: Array<{ content: string }> = [],
  iteration = 1
): Promise<{
  status: "PASS" | "ASK" | "LOCK_FAIL";
  question?: string;
  iteration: number;
  missingCriteria: GateFailureReason[];
  confidence: number;
}> {
  try {
    const { anthropic } = await import("@ai-sdk/anthropic");
    const { generateText } = await import("ai");

    const allText = [userInput, ...previousMessages.map(m => m.content)].join("\n");

    const prompt = `Analyze this product request for specificity across 4 required criteria. All must be satisfied to PASS:

1. **Target User/Persona**: Must have explicit target user/persona matching pattern: (for|to)\\s+(?:busy|early|solo|small|enterprise|[a-z]+)\\s+(?:founders?|marketers?|developers?)
2. **Core Feature**: Must have singular core feature statement detected by "the core feature is" or one bullet point
3. **Success Metrics**: Must contain measurable success metric (%, $, "KPI", "metric", or quantifier)
4. **Problem Statement**: Must have problem statement < 160 chars starting with verb ("struggle", "need", "cannot")

INPUT TO ANALYZE:
${allText}

CURRENT ITERATION: ${iteration}/3

Return ONLY a JSON object with this exact structure:
{
  "status": "PASS" | "ASK" | "LOCK_FAIL",
  "missingCriteria": ["missing_target_user", "missing_core_feature", "missing_success_metrics", "missing_problem_statement"],
  "confidence": 0.85,
  "primaryMissing": "missing_target_user"
}

Rules:
- Return "PASS" if ALL 4 criteria are satisfied
- Return "ASK" if any criteria are missing and iteration < 3
- Return "LOCK_FAIL" if criteria are missing and iteration >= 3
- Include only missing criteria in the array
- Confidence should be 0-1 based on how well criteria are met`;

    const result = await generateText({
      model: anthropic('claude-3-5-sonnet-20241022'),
      prompt,
      temperature: 0.3,
      maxTokens: 500,
    });

    // Parse Claude's response
    const analysis = JSON.parse(result.text);

    // Generate question if needed
    let question: string | undefined;
    if (analysis.status === "ASK" && analysis.primaryMissing) {
      const questions = CLARIFICATION_QUESTIONS[analysis.primaryMissing as keyof typeof CLARIFICATION_QUESTIONS] || [];
      const questionIndex = (iteration - 1) % questions.length;
      question = questions[questionIndex] || questions[0];
    }

    return {
      status: analysis.status,
      question,
      iteration,
      missingCriteria: analysis.missingCriteria || [],
      confidence: analysis.confidence || 0
    };

  } catch (error) {
    console.error("Claude analysis error:", error);

    // Fallback to simple heuristic analysis
    return {
      status: iteration >= 3 ? "LOCK_FAIL" : "ASK",
      question: "I need more specific details about your product requirements.",
      iteration,
      missingCriteria: ["missing_problem_statement"],
      confidence: 0.3
    };
  }
}

/**
 * Get or create refinement session for conversation
 */
async function getRefinementSession(
  ctx: MutationCtx,
  conversationId: Id<"conversations">
): Promise<{ iteration: number; lastQuestion?: string }> {
  const existing = await ctx.db
    .query("refinement_sessions")
    .withIndex("by_conversation", (q) => q.eq("conversationId", conversationId))
    .first();

  if (existing) {
    return {
      iteration: existing.iteration,
      lastQuestion: existing.lastQuestion
    };
  }

  // Create new session
  await ctx.db.insert("refinement_sessions", {
    conversationId,
    iteration: 1,
    createdAt: Date.now(),
    updatedAt: Date.now()
  });

  return { iteration: 1 };
}

/**
 * Update refinement session with new iteration and question
 */
async function updateRefinementSession(
  ctx: MutationCtx,
  conversationId: Id<"conversations">,
  iteration: number,
  lastQuestion?: string
): Promise<void> {
  const existing = await ctx.db
    .query("refinement_sessions")
    .withIndex("by_conversation", (q) => q.eq("conversationId", conversationId))
    .first();

  if (existing) {
    await ctx.db.patch(existing._id, {
      iteration,
      lastQuestion,
      updatedAt: Date.now()
    });
  } else {
    await ctx.db.insert("refinement_sessions", {
      conversationId,
      iteration,
      lastQuestion,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });
  }
}

/**
 * Internal specificity gate function for cascade workflow integration
 */
export async function specificityGateInternal(
  ctx: MutationCtx,
  context: GateContext
): Promise<DetailedGateResult> {
  const startTime = Date.now();

  try {
    // Get current refinement session
    const session = await getRefinementSession(ctx, context.conversationId);

    // Analyze specificity with Claude
    const analysis = await analyzeSpecificityWithClaude(
      context.userInput,
      context.previousMessages,
      session.iteration
    );

    const processingTime = Date.now() - startTime;

    if (analysis.status === "PASS") {
      // All criteria met - PASS
      await ctx.db.insert("gate_results", {
        conversationId: context.conversationId,
        gate: "specificity",
        status: "pass",
        reasons: [], // Empty array for successful gates
        createdAt: Date.now()
      });

      return {
        status: "pass",
        message: "Perfect! I have all the details needed to create your PRD. All requirements are clear and specific.",
        confidence: analysis.confidence,
        processingTime,
        metadata: {
          iteration: analysis.iteration,
          missingCriteria: 0
        }
      };
    } else if (analysis.status === "LOCK_FAIL") {
      // Refinement loop exhausted - LOCK_FAIL
      await ctx.db.insert("gate_results", {
        conversationId: context.conversationId,
        gate: "specificity",
        status: "fail",
        reasons: ["refinement_loop_exhausted"],
        createdAt: Date.now()
      });

      return {
        status: "fail",
        message: "I've tried to gather more details, but we still need more specificity. Let's proceed with what we have and refine as we go. You can always provide more details later.",
        failureReasons: ["refinement_loop_exhausted"],
        confidence: analysis.confidence,
        processingTime,
        metadata: {
          iteration: analysis.iteration,
          maxIterationsReached: true,
          missingCriteria: analysis.missingCriteria.length
        }
      };
    } else {
      // Need more clarification - ASK
      const nextIteration = session.iteration + 1;

      // Update refinement session
      await updateRefinementSession(
        ctx,
        context.conversationId,
        nextIteration,
        analysis.question
      );

      // Store the current state for next iteration
      await ctx.db.insert("gate_results", {
        conversationId: context.conversationId,
        gate: "specificity",
        status: "fail",
        reasons: analysis.missingCriteria,
        createdAt: Date.now()
      });

      return {
        status: "fail",
        message: analysis.question || "I need more specific details about your product requirements.",
        failureReasons: analysis.missingCriteria,
        confidence: analysis.confidence,
        processingTime,
        metadata: {
          iteration: analysis.iteration,
          needsRefinement: true,
          missingCriteria: analysis.missingCriteria.length,
          nextIteration
        }
      };
    }
  } catch (error) {
    // Log error and return failure
    console.error("Specificity gate error:", error);

    return {
      status: "fail",
      message: "Internal error occurred while analyzing specificity. Please try again.",
      failureReasons: ["refinement_loop_exhausted"],
      confidence: 0,
      processingTime: Date.now() - startTime,
      metadata: {
        error: "internal_error"
      }
    };
  }
}

/**
 * Specificity Gate Convex mutation
 *
 * Analyzes user input for specificity and returns pass/fail result with refinement loop.
 * Converts to proper Convex mutation as specified in task requirements.
 */
export const specificityGate = mutation({
  args: {
    conversationId: v.id("conversations"),
    message: v.string()
  },
  handler: async (ctx, { conversationId, message }): Promise<DetailedGateResult> => {
    const gateContext: GateContext = {
      conversationId,
      userInput: message,
      previousMessages: [], // Will be populated by cascade workflow
      priorGateResults: {}
    };

    return await specificityGateInternal(ctx, gateContext);
  }
});
