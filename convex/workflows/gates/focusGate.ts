/**
 * Focus Gate - Second gate in the cascade flow
 *
 * Detects when user input mixes multiple products or disparate ideas.
 * Fails when the request spans multiple domains or compares different products.
 */

import { mutation } from "../../_generated/server";
import { v } from "convex/values";
import type { MutationCtx } from "../../_generated/server";
import {
  type DetailedGateResult,
  type GateContext,
  type GateFailureReason
} from "../sharedTypes";

/**
 * Patterns that indicate multi-product or unfocused requests
 */
const MULTI_FOCUS_PATTERNS = {
  // Multiple products explicitly mentioned
  multipleProducts: {
    pattern: /\b(app|application|platform|tool|service|system|software|website|portal)\s+(and|or|plus|&|\+)\s+(app|application|platform|tool|service|system|software|website|portal)/gi,
    reason: "multiple_products_detected" as GateFailureReason,
    weight: 0.9
  },

  // Comparison requests
  comparisonRequests: {
    pattern: /\b(compare|versus|vs\.?|difference between|which is better|pros and cons of)\b/gi,
    reason: "comparison_request" as GateFailureReason,
    weight: 0.8
  },

  // Multiple domains/industries
  multipleDomains: {
    pattern: /\b(e-commerce|ecommerce)\b.*\b(healthcare|education|finance|banking|retail|manufacturing|logistics|real estate|entertainment|gaming|social media|marketing|hr|crm)\b/gi,
    reason: "multiple_domains" as GateFailureReason,
    weight: 0.7
  },

  // Enumerated lists (bullet-like patterns)
  enumeratedLists: {
    pattern: /(\d+\.|•|-)[\s\S]*?(\d+\.|•|-)[\s\S]*?(\d+\.|•|-)/g,
    reason: "disparate_ideas_mixed" as GateFailureReason,
    weight: 0.6
  },

  // Platform combinations (mobile + web + desktop)
  platformCombinations: {
    pattern: /\b(mobile|ios|android)\b.*\b(web|website|browser)\b.*\b(desktop|windows|mac|linux)\b/gi,
    reason: "multiple_products_detected" as GateFailureReason,
    weight: 0.7
  }
};

/**
 * Analyzes user input for focus issues
 */
function analyzeFocus(userInput: string): {
  isFocused: boolean;
  failureReasons: GateFailureReason[];
  confidence: number;
  detectedPatterns: Array<{ pattern: string; snippet: string; reason: GateFailureReason }>;
  primaryTopic?: string;
  additionalTopics?: string[];
} {
  const detectedPatterns: Array<{ pattern: string; snippet: string; reason: GateFailureReason }> = [];
  let totalWeight = 0;
  const maxWeight = Math.max(...Object.values(MULTI_FOCUS_PATTERNS).map(p => p.weight));

  // Check each pattern
  for (const [patternName, config] of Object.entries(MULTI_FOCUS_PATTERNS)) {
    const matches = userInput.match(config.pattern);
    if (matches) {
      for (const match of matches) {
        detectedPatterns.push({
          pattern: patternName,
          snippet: match,
          reason: config.reason
        });
        totalWeight = Math.max(totalWeight, config.weight);
      }
    }
  }

  // Extract topics for binary choice generation
  const topics = extractTopics(userInput);

  // Calculate confidence and determine if focused
  const confidence = totalWeight / maxWeight;
  const isFocused = confidence <= 0.5 && detectedPatterns.length < 2;

  const failureReasons = [...new Set(detectedPatterns.map(p => p.reason))];

  return {
    isFocused,
    failureReasons,
    confidence,
    detectedPatterns,
    primaryTopic: topics.length > 0 ? topics[0] : undefined,
    additionalTopics: topics.length > 1 ? topics.slice(1) : undefined
  };
}

/**
 * Extracts potential topics/products from user input
 */
function extractTopics(userInput: string): string[] {
  const topicPatterns = [
    /\b(mobile app|web app|website|desktop app|platform|tool|service|system|software|portal|dashboard|api|database)\b/gi,
    /\b(e-commerce|ecommerce|marketplace|store|shop|retail)\b/gi,
    /\b(social media|social network|chat|messaging|communication)\b/gi,
    /\b(crm|cms|erp|hrms|project management|task management)\b/gi,
    /\b(analytics|reporting|dashboard|monitoring|tracking)\b/gi
  ];

  const topics: string[] = [];

  for (const pattern of topicPatterns) {
    const matches = userInput.match(pattern);
    if (matches) {
      topics.push(...matches.map(match => match.toLowerCase()));
    }
  }

  // Remove duplicates and return first 3
  return [...new Set(topics)].slice(0, 3);
}

/**
 * Focus-specific failure reasons used by this gate
 */
type FocusGateFailureReason =
  | "multiple_products_detected"
  | "comparison_request"
  | "multiple_domains"
  | "disparate_ideas_mixed";

/**
 * Generates binary choice message for multi-focus requests
 */
function generateBinaryChoiceMessage(
  detectedPatterns: Array<{ pattern: string; snippet: string; reason: GateFailureReason }>,
  primaryTopic?: string,
  additionalTopics?: string[]
): string {
  const reasonMessages: Record<FocusGateFailureReason, string> = {
    multiple_products_detected: "I notice you're mentioning multiple products or platforms. To give you the best help, let's focus on one at a time.",
    comparison_request: "It looks like you want to compare different options. Let's focus on one specific solution first.",
    multiple_domains: "Your request spans multiple industries or domains. Let's narrow down to one specific area.",
    disparate_ideas_mixed: "I see several different ideas in your request. Let's tackle them one by one."
  };

  // Get the primary reason and ensure it's a focus-specific reason
  const primaryReason = detectedPatterns[0]?.reason;
  const baseMessage = primaryReason && primaryReason in reasonMessages
    ? reasonMessages[primaryReason as FocusGateFailureReason]
    : "Your request covers multiple topics.";

  // If we have specific topics, create a binary choice
  if (primaryTopic && additionalTopics && additionalTopics.length > 0) {
    const topicA = primaryTopic;
    const topicB = additionalTopics[0];
    return `${baseMessage} Which would you like to focus on first?\n\nA) ${topicA}\nB) ${topicB}\n\nPlease reply with 'A' or 'B' to continue.`;
  }

  return `${baseMessage} Could you pick one specific product or idea to focus on first? We can address the others separately afterward.`;
}

/**
 * Internal helper function for use within the cascade workflow
 * Maintains backward compatibility with existing cascade workflow
 */
export async function focusGateInternal(
  ctx: MutationCtx,
  context: GateContext
): Promise<DetailedGateResult> {
    const startTime = Date.now();

    try {
      // Analyze the user input for focus issues
      const analysis = analyzeFocus(context.userInput);

      const processingTime = Date.now() - startTime;

      if (!analysis.isFocused) {
        // Generate binary choice message
        const binaryChoiceMessage = generateBinaryChoiceMessage(
          analysis.detectedPatterns,
          analysis.primaryTopic,
          analysis.additionalTopics
        );

        // Store gate result in database
        await ctx.db.insert("gate_results", {
          conversationId: context.conversationId,
          gate: "focus",
          status: "fail",
          reasons: analysis.failureReasons,
          createdAt: Date.now()
        });

        return {
          status: "fail",
          message: binaryChoiceMessage,
          failureReasons: analysis.failureReasons,
          confidence: analysis.confidence,
          processingTime,
          metadata: {
            detectedPatterns: analysis.detectedPatterns.length,
            primaryTopic: analysis.primaryTopic || "unknown",
            additionalTopics: analysis.additionalTopics?.length || 0
          }
        };
      } else {
        // Store successful gate result
        await ctx.db.insert("gate_results", {
          conversationId: context.conversationId,
          gate: "focus",
          status: "pass",
          reasons: [], // Empty array for successful gates
          createdAt: Date.now()
        });

        return {
          status: "pass",
          message: "Request has clear single focus.",
          confidence: 1 - analysis.confidence, // Invert confidence for pass case
          processingTime,
          metadata: {
            detectedPatterns: 0,
            primaryTopic: analysis.primaryTopic || "single_focus"
          }
        };
      }
    } catch (error) {
      // Log error and return failure
      console.error("Focus gate error:", error);

      return {
        status: "fail",
        message: "Internal error occurred while analyzing your request focus. Please try again.",
        confidence: 0,
        processingTime: Date.now() - startTime,
        metadata: {
          error: "internal_error"
        }
      };
    }
}

/**
 * Focus Gate Convex mutation
 *
 * Analyzes user input for focus issues and returns pass/fail result with binary choice if needed.
 * Converts to proper Convex mutation as specified in task requirements.
 */
export const focusGate = mutation({
  args: {
    conversationId: v.id("conversations"),
    message: v.string()
  },
  handler: async (ctx, { conversationId, message }): Promise<DetailedGateResult> => {
    try {
      // Validate conversation exists
      const conversation = await ctx.db.get(conversationId);
      if (!conversation) {
        throw new Error("Conversation not found");
      }

      // Create context for the internal function
      const context: GateContext = {
        userInput: message,
        conversationId,
        previousMessages: [],
        priorGateResults: {}
      };

      // Call the internal function
      return await focusGateInternal(ctx, context);
    } catch (error) {
      // Log error and return failure
      console.error("Focus gate mutation error:", error);

      return {
        status: "fail",
        message: "Internal error occurred while analyzing your request focus. Please try again.",
        confidence: 0,
        processingTime: 0,
        metadata: {
          error: "internal_error"
        }
      };
    }
  }
});
