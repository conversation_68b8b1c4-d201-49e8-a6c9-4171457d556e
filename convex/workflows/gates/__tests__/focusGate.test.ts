/**
 * Basic unit tests for Focus Gate
 *
 * Minimal tests to verify core functionality.
 * Comprehensive tests will be added in task 5.9.
 */

import { describe, expect, test } from "vitest";

import type { Id } from "../../../_generated/dataModel";
import type { MutationCtx } from "../../../_generated/server";
import type { GateContext } from "../../sharedTypes";
import { focusGateInternal } from "../focusGate";

// Helper function to create mock context
// Note: This is a minimal mock for basic sanity tests.
// Comprehensive tests with full type safety will be added in task 5.9.
function createMockContext(): MutationCtx {
  return {
    db: {
      insert: async () => "mock-id" as Id<any>,
      get: async () => ({ id: "mock-conversation", userId: "mock-user" })
    }
  } as unknown as MutationCtx;
}

describe("Focus Gate - Basic Tests", () => {
  const mockConversationId = "test-conversation-id" as Id<"conversations">;

  test("should pass for single-product input", async () => {
    const mockCtx = createMockContext();
    const context: GateContext = {
      userInput: "I need a mobile app to help users track their daily water intake and send reminders.",
      conversationId: mockConversationId,
      previousMessages: [],
      priorGateResults: {}
    };

    const result = await focusGateInternal(mockCtx, context);

    expect(result.status).toBe("pass");
    expect(result.message).toBe("Request has clear single focus.");
  });

  test("should fail for multi-product input containing 'and'", async () => {
    const mockCtx = createMockContext();
    const context: GateContext = {
      userInput: "I want to build a mobile app and a web platform and a desktop application for project management.",
      conversationId: mockConversationId,
      previousMessages: [],
      priorGateResults: {}
    };

    const result = await focusGateInternal(mockCtx, context);

    expect(result.status).toBe("fail");
    expect(result.failureReasons).toBeDefined();
    expect(result.failureReasons).toContain("multiple_products_detected");
    expect(result.message).toContain("multiple products");
  });

  test("should fail for comparison requests", async () => {
    const mockCtx = createMockContext();
    const context: GateContext = {
      userInput: "Compare mobile apps vs web applications for e-commerce. Which is better?",
      conversationId: mockConversationId,
      previousMessages: [],
      priorGateResults: {}
    };

    const result = await focusGateInternal(mockCtx, context);

    expect(result.status).toBe("fail");
    expect(result.failureReasons).toBeDefined();
    expect(result.failureReasons).toContain("comparison_request");
    expect(result.message).toContain("compare different options");
  });
});
