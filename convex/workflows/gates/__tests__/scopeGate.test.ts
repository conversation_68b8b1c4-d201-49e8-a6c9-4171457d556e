/**
 * Basic unit tests for Scope Gate
 *
 * Minimal tests to verify core functionality.
 * Comprehensive tests will be added in task 5.9.
 */

import { describe, expect, test } from "vitest";

import type { Id } from "../../../_generated/dataModel";
import type { MutationCtx } from "../../../_generated/server";
import type { GateContext } from "../../sharedTypes";
import { scopeGateInternal } from "../scopeGate";

// Helper function to create mock context
// Note: This is a minimal mock for basic sanity tests.
// Comprehensive tests with full type safety will be added in task 5.9.
function createMockContext(): MutationCtx {
  return {
    db: {
      insert: async () => "mock-id" as Id<any>,
      get: async () => ({ id: "mock-conversation", userId: "mock-user" })
    }
  } as unknown as MutationCtx;
}

describe("Scope Gate - Basic Tests", () => {
  const mockConversationId = "test-conversation-id" as Id<"conversations">;

  test("should pass for appropriately scoped request", async () => {
    const mockCtx = createMockContext();
    const context: GateContext = {
      userInput: "I need a simple app to track daily water intake with reminders and basic progress tracking.",
      conversationId: mockConversationId,
      previousMessages: [],
      priorGateResults: {}
    };

    const result = await scopeGateInternal(mockCtx, context);

    expect(result.status).toBe("pass");
    expect(result.message).toBe("Request has appropriate scope for an MLP.");
  });

  test("should fail for too many features", async () => {
    const mockCtx = createMockContext();
    const context: GateContext = {
      userInput: "I want an app with user login, dashboard, profile management, settings, notifications, analytics, reporting, chat, messaging, calendar, booking, payments, and admin panel features.",
      conversationId: mockConversationId,
      previousMessages: [],
      priorGateResults: {}
    };

    const result = await scopeGateInternal(mockCtx, context);

    expect(result.status).toBe("fail");
    expect(result.failureReasons).toBeDefined();
    expect(result.failureReasons).toContain("too_many_features");
    expect(result.message).toContain("features");
  });

  test("should fail for multiple core functions", async () => {
    const mockCtx = createMockContext();
    const context: GateContext = {
      userInput: "The core feature is user management and the main function is data analytics and the primary capability is reporting.",
      conversationId: mockConversationId,
      previousMessages: [],
      priorGateResults: {}
    };

    const result = await scopeGateInternal(mockCtx, context);

    expect(result.status).toBe("fail");
    expect(result.failureReasons).toBeDefined();
    expect(result.failureReasons).toContain("multiple_core_functions");
    expect(result.message).toContain("core");
  });

  test("should fail for ecosystem-scale language", async () => {
    const mockCtx = createMockContext();
    const context: GateContext = {
      userInput: "I want to build a platform with marketplace functionality and third-party integrations with an SDK for developers.",
      conversationId: mockConversationId,
      previousMessages: [],
      priorGateResults: {}
    };

    const result = await scopeGateInternal(mockCtx, context);

    expect(result.status).toBe("fail");
    expect(result.failureReasons).toBeDefined();
    expect(result.failureReasons).toContain("ecosystem_scale_detected");
    expect(result.message).toContain("platform");
  });

  test("should fail for kitchen sink approach", async () => {
    const mockCtx = createMockContext();
    const context: GateContext = {
      userInput: "I want everything possible in one comprehensive all-in-one solution that covers all user needs.",
      conversationId: mockConversationId,
      previousMessages: [],
      priorGateResults: {}
    };

    const result = await scopeGateInternal(mockCtx, context);

    expect(result.status).toBe("fail");
    expect(result.failureReasons).toBeDefined();
    expect(result.failureReasons).toContain("kitchen_sink_approach");
    expect(result.message).toContain("essential features");
  });

  test("should fail for scope creep language", async () => {
    const mockCtx = createMockContext();
    const context: GateContext = {
      userInput: "I need a simple todo app, and also user management, plus analytics, furthermore we need reporting, additionally chat features.",
      conversationId: mockConversationId,
      previousMessages: [],
      priorGateResults: {}
    };

    const result = await scopeGateInternal(mockCtx, context);

    expect(result.status).toBe("fail");
    expect(result.failureReasons).toBeDefined();
    expect(result.failureReasons).toContain("scope_creep");
    expect(result.message).toContain("boundaries");
  });
});
