/**
 * Lightweight smoke tests for Specificity Gate
 * 
 * Purpose: Dependency verification and basic correctness
 * Exhaustive scenarios will be implemented in task 5.9
 */

import { describe, it, expect, beforeEach } from "vitest";
import { convexTest } from "convex-test";
import { api } from "../../../_generated/api";
import schema from "../../../schema";

describe("Specificity Gate - Smoke Tests", () => {
  let t: any;

  beforeEach(async () => {
    t = convexTest(schema);
  });

  it("should PASS for input that satisfies all heuristics", async () => {
    // Create a conversation
    const conversationId = await t.mutation(api.chat.createConversation, {
      title: "Test Conversation",
      mode: "clarification"
    });

    // Test input that should satisfy all specificity criteria
    const specificInput = `
      I want to build a time tracking app for busy freelance developers.
      The core feature is automatic time tracking that detects when users are coding.
      The problem is that freelancers struggle to accurately track billable hours.
      Success will be measured by 25% increase in billing accuracy and $5k monthly revenue.
    `;

    const result = await t.mutation(api.workflows.gates.specificityGate.specificityGate, {
      conversationId,
      message: specificInput
    });

    expect(result.status).toBe("pass");
    expect(result.message).toContain("Perfect! I have all the details needed");
    expect(result.confidence).toBeGreaterThan(0.8);
  });

  it("should ASK for generic/vague input that misses heuristics", async () => {
    // Create a conversation
    const conversationId = await t.mutation(api.chat.createConversation, {
      title: "Test Conversation",
      mode: "clarification"
    });

    // Test input that is too vague and misses specificity criteria
    const vagueInput = "I want to build an app that helps people be more productive";

    const result = await t.mutation(api.workflows.gates.specificityGate.specificityGate, {
      conversationId,
      message: vagueInput
    });

    expect(result.status).toBe("fail");
    expect(result.message).toBeDefined();
    expect(result.failureReasons).toBeDefined();
    expect(result.failureReasons!.length).toBeGreaterThan(0);
    expect(result.metadata?.needsRefinement).toBe(true);
  });

  it("should handle edge case gracefully (empty string)", async () => {
    // Create a conversation
    const conversationId = await t.mutation(api.chat.createConversation, {
      title: "Test Conversation",
      mode: "clarification"
    });

    // Test edge case with empty string
    const emptyInput = "";

    const result = await t.mutation(api.workflows.gates.specificityGate.specificityGate, {
      conversationId,
      message: emptyInput
    });

    // Should handle gracefully - either LOCK_FAIL or ASK with proper error handling
    expect(result.status).toBe("fail");
    expect(result.message).toBeDefined();
    expect(typeof result.message).toBe("string");
    expect(result.confidence).toBeDefined();
    expect(result.confidence).toBeGreaterThanOrEqual(0);
    expect(result.confidence).toBeLessThanOrEqual(1);
  });
});
