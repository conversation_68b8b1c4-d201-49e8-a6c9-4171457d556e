/**
 * Lightweight smoke tests for Specificity Gate
 *
 * Purpose: Dependency verification and basic correctness
 * Exhaustive scenarios will be implemented in task 5.9
 */

import { describe, expect, test, vi } from "vitest";

import type { Id } from "../../../_generated/dataModel";
import type { MutationCtx } from "../../../_generated/server";
import type { GateContext } from "../../sharedTypes";
import { specificityGateInternal } from "../specificityGate";

// Mock the Claude API calls for testing
vi.mock("@ai-sdk/anthropic", () => ({
  anthropic: () => ({})
}));

vi.mock("ai", () => ({
  generateText: vi.fn()
}));

// Helper function to create mock context
// Note: This is a minimal mock for basic sanity tests.
// Comprehensive tests with full type safety will be added in task 5.9.
function createMockContext(): MutationCtx {
  return {
    db: {
      insert: async () => "mock-id" as Id<"refinement_sessions">,
      get: async () => ({ id: "mock-conversation", userId: "mock-user" }),
      query: () => ({
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        withIndex: (_indexName: string, _fn: any) => ({
          first: async () => null // No existing refinement session
        })
      }),
      patch: async () => {}
    }
  } as unknown as MutationCtx;
}

describe("Specificity Gate - Smoke Tests", () => {
  const mockConversationId = "test-conversation-id" as Id<"conversations">;

  test("should PASS for input that satisfies all heuristics", async () => {
    // Mock Claude to return PASS
    const { generateText } = await import("ai");
    vi.mocked(generateText).mockResolvedValue({
      text: JSON.stringify({
        status: "PASS",
        missingCriteria: [],
        confidence: 0.9,
        primaryMissing: null
      })
    } as any);

    const mockCtx = createMockContext();
    const context: GateContext = {
      userInput: `I want to build a time tracking app for busy freelance developers.
        The core feature is automatic time tracking that detects when users are coding.
        The problem is that freelancers struggle to accurately track billable hours.
        Success will be measured by 25% increase in billing accuracy and $5k monthly revenue.`,
      conversationId: mockConversationId,
      previousMessages: [],
      priorGateResults: {}
    };

    const result = await specificityGateInternal(mockCtx, context);

    expect(result.status).toBe("pass");
    expect(result.message).toContain("Perfect! I have all the details needed");
    expect(result.confidence).toBeGreaterThan(0.8);
  });

  test("should ASK for generic/vague input that misses heuristics", async () => {
    // Mock Claude to return ASK
    const { generateText } = await import("ai");
    vi.mocked(generateText).mockResolvedValue({
      text: JSON.stringify({
        status: "ASK",
        missingCriteria: ["missing_target_user", "missing_core_feature"],
        confidence: 0.3,
        primaryMissing: "missing_target_user"
      })
    } as any);

    const mockCtx = createMockContext();
    const context: GateContext = {
      userInput: "I want to build an app that helps people be more productive",
      conversationId: mockConversationId,
      previousMessages: [],
      priorGateResults: {}
    };

    const result = await specificityGateInternal(mockCtx, context);

    expect(result.status).toBe("fail");
    expect(result.message).toBeDefined();
    expect(result.failureReasons).toBeDefined();
    expect(result.failureReasons?.length).toBeGreaterThan(0);
    expect(result.metadata?.needsRefinement).toBe(true);
  });

  test("should handle edge case gracefully (empty string)", async () => {
    // Mock Claude to simulate error/fallback behavior
    const { generateText } = await import("ai");
    vi.mocked(generateText).mockRejectedValue(new Error("Invalid input"));

    const mockCtx = createMockContext();
    const context: GateContext = {
      userInput: "",
      conversationId: mockConversationId,
      previousMessages: [],
      priorGateResults: {}
    };

    const result = await specificityGateInternal(mockCtx, context);

    // Should handle gracefully - either LOCK_FAIL or ASK with proper error handling
    expect(result.status).toBe("fail");
    expect(result.message).toBeDefined();
    expect(typeof result.message).toBe("string");
    expect(result.confidence).toBeDefined();
    expect(result.confidence).toBeGreaterThanOrEqual(0);
    expect(result.confidence).toBeLessThanOrEqual(1);
  });
});
