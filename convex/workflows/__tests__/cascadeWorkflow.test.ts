/**
 * Basic integration tests for Cascade Workflow
 *
 * Tests the integration of the scope gate with the cascade workflow.
 * Comprehensive tests will be added in task 5.10.
 */

import { describe, expect, test } from "vitest";

import type { Id } from "../../_generated/dataModel";
import type { MutationCtx } from "../../_generated/server";
import { executeCascadeInternal } from "../cascadeWorkflow";

// Helper function to create mock context
function createMockContext(): MutationCtx {
  return {
    db: {
      insert: async () => "mock-id" as Id<any>,
      get: async () => ({ id: "mock-conversation", userId: "mock-user" })
    }
  } as unknown as MutationCtx;
}

describe("Cascade Workflow - Scope Gate Integration", () => {
  const mockConversationId = "test-conversation-id" as Id<"conversations">;

  test("should pass through all gates for well-scoped input", async () => {
    const mockCtx = createMockContext();

    const result = await executeCascadeInternal(
      mockCtx,
      mockConversationId,
      "I need a simple water tracking app that reminds users to drink water and shows their daily progress.",
      []
    );

    expect(result.status).toBe("pass");
    expect(result.gateResults).toHaveProperty("vagueness");
    expect(result.gateResults).toHaveProperty("focus");
    expect(result.gateResults).toHaveProperty("scope");
    expect(result.gateResults.vagueness.status).toBe("pass");
    expect(result.gateResults.focus.status).toBe("pass");
    expect(result.gateResults.scope.status).toBe("pass");
  });

  test("should fail at scope gate for overly complex request", async () => {
    const mockCtx = createMockContext();

    const result = await executeCascadeInternal(
      mockCtx,
      mockConversationId,
      "I want a comprehensive platform with user management, analytics, reporting, chat, messaging, calendar, booking, payments, admin panel, and everything else possible.",
      []
    );

    expect(result.status).toBe("fail");
    expect(result.failedAt).toBe("scope");
    expect(result.gateResults).toHaveProperty("vagueness");
    expect(result.gateResults).toHaveProperty("focus");
    expect(result.gateResults).toHaveProperty("scope");
    expect(result.gateResults.vagueness.status).toBe("pass");
    expect(result.gateResults.focus.status).toBe("pass");
    expect(result.gateResults.scope.status).toBe("fail");
    expect(result.message).toContain("features");
  });

  test("should fail at vagueness gate before reaching scope gate", async () => {
    const mockCtx = createMockContext();

    const result = await executeCascadeInternal(
      mockCtx,
      mockConversationId,
      "Build an AI SaaS with ML algorithms and REST APIs.",
      []
    );

    expect(result.status).toBe("fail");
    expect(result.failedAt).toBe("vagueness");
    expect(result.gateResults).toHaveProperty("vagueness");
    expect(result.gateResults.vagueness.status).toBe("fail");
    // Should not have reached scope gate
    expect(result.gateResults).not.toHaveProperty("scope");
  });

  test("should fail at focus gate before reaching scope gate", async () => {
    const mockCtx = createMockContext();

    const result = await executeCascadeInternal(
      mockCtx,
      mockConversationId,
      "I want to compare different apps and build multiple applications for various platforms.",
      []
    );

    expect(result.status).toBe("fail");
    expect(result.failedAt).toBe("focus");
    expect(result.gateResults).toHaveProperty("vagueness");
    expect(result.gateResults).toHaveProperty("focus");
    expect(result.gateResults.vagueness.status).toBe("pass");
    expect(result.gateResults.focus.status).toBe("fail");
    // Should not have reached scope gate
    expect(result.gateResults).not.toHaveProperty("scope");
  });
});
